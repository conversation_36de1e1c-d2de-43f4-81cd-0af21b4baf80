import {
  BrowserDataAdapter,
  BrowserSession,
  RemoteBrowserService,
} from '../adapters/BrowserDataAdapter';
import { BrowserServiceFactory } from './BrowserServiceFactory';
import { BrowserStateService } from '../BrowserStateService';
import { CoordinatorDOBrowserStateRepository } from '../CoordinatorDOBrowserStateRepository';
import { ConnectionsWorkflowParams } from '../types';
import { CDP } from '../../browser/simple-cdp';
import { getHashedScriptUrl, wrapForMainFrameOnly } from '../../browser';
import { ErrorService } from '../../common/error';
import { K_CUSTOM_VIEWPORT } from '../utils/constants';
import { platformDetails } from '../../ui/constants';
import { CDPBrowserDataAdapter } from '../adapters/CDPBrowserDataAdapter';

export class BrowserManager {
  // Logging configuration
  private config = {
    debug: true, // Set to true for verbose logging
  };

  private env: Env;
  private eventPayload: ConnectionsWorkflowParams;

  private browserService: RemoteBrowserService;
  private browserStateService: BrowserStateService;
  private browserDataAdapter?: BrowserDataAdapter;

  constructor({ env, event }: { env: Env; event: ConnectionsWorkflowParams }) {
    this.eventPayload = event;
    this.env = env;
    this.browserService = BrowserServiceFactory.createFromEnvironment(env);
    this.browserStateService = new BrowserStateService(
      new CoordinatorDOBrowserStateRepository(this.env.CoordinatorDO),
    );
  }

  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[kazeel][browser-manager]', ...args);
    }
  }

  private warn(...args: any[]): void {
    console.warn('[kazeel][browser-manager]', ...args);
  }

  private error(...args: any[]): void {
    console.error('[kazeel][browser-manager]', ...args);
  }

  async createCDPSession(): Promise<{ cdp: CDP; browserSession: BrowserSession }> {
    try {
      const browserSession = await this.browserService.getSession(this.eventPayload.sessionId);

      const cdp = new CDP({ webSocketDebuggerUrl: browserSession.wsEndpoint });

      await cdp.Target.setAutoAttach({
        autoAttach: true,
        flatten: true,
        waitForDebuggerOnStart: false,
      });

      return { cdp, browserSession };
    } catch (error) {
      await this.handleBrowserError(error);
      throw error;
    }
  }

  async setupBrowserSession(cdp: CDP) {
    try {
      // Set up two-tab architecture
      // Create control tab first
      const controlTabTarget = await cdp.Target.createTarget({ url: 'about:blank' });

      // Create target tab
      const mainTargetResponse = await cdp.Target.createTarget({ url: 'about:blank' });

      return {
        targetId: mainTargetResponse.targetId,
        controlTabTargetId: controlTabTarget.targetId,
      };
    } catch (error) {
      await this.handleBrowserError(error);
      throw error;
    }
  }

  async setupTwoTabArchitecture({
    cdp,
    targetId,
    controlTabTargetId,
    browserSessionWSEndpoint,
  }: {
    cdp: CDP;
    targetId: string;
    controlTabTargetId: string;
    browserSessionWSEndpoint: string;
  }) {
    try {
      const sessionIds = await this.attachControlAndTargetTabs({
        cdp: cdp,
        targetId: targetId,
        controlTabTargetId: controlTabTargetId,
      });

      await this.injectControlTabScriptsEarly({
        cdp: cdp,
        targetId: targetId,
        controlTabSessionId: sessionIds.controlTabSessionId,
        browserSessionWSEndpoint: browserSessionWSEndpoint,
      });

      await this.injectBrowserControllerProxyInTarget({
        cdp: cdp,
        targetSessionId: sessionIds.targetSessionId,
      });

      //Load browser state, if available
      if (!this.browserDataAdapter) {
        this.browserDataAdapter = new CDPBrowserDataAdapter(cdp, sessionIds.targetSessionId);
      }

      await this.browserStateService.loadBrowserStateToPage(
        this.browserDataAdapter,
        this.eventPayload.userId,
        this.eventPayload.platformId,
      );

      return sessionIds;
    } catch (error) {
      this.log(`An error occurred ${JSON.stringify(error)}`);
      await this.handleBrowserError(error);
      throw error;
    }
  }

  /**
   * Ensures the viewport and device metrics are set to the correct dimensions
   * This should be called after navigation or any time we need to guarantee viewport consistency
   */
  async ensureViewportSettings({
    cdp,
    targetSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
  }): Promise<void> {
    await cdp.Emulation.setDeviceMetricsOverride(
      {
        //store these to constants
        width: K_CUSTOM_VIEWPORT.width,
        height: K_CUSTOM_VIEWPORT.height,
        deviceScaleFactor: 1,
        mobile: false,
      },
      targetSessionId,
    );
  }

  async navigateToLoginPage({
    cdp,
    targetSessionId,
    controlTabSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
    controlTabSessionId: string;
  }): Promise<void> {
    // goto github.com on controlTapSessionId
    const pageLoadControlTab = this.waitForPageLoad({ cdp: cdp, targetSessionId: targetSessionId });
    await cdp.Page.navigate(
      {
        url: platformDetails[this.eventPayload.platformId].loginLink,
      },
      controlTabSessionId,
    );

    await pageLoadControlTab;

    const pageLoad = this.waitForPageLoad({ cdp: cdp, targetSessionId: targetSessionId });

    await cdp.Page.navigate(
      {
        url: platformDetails[this.eventPayload.platformId].loginLink,
      },
      targetSessionId,
    );

    await pageLoad;
  }

  async setupExecutionContextListener({
    cdp,
    targetId,
  }: {
    cdp: CDP;
    targetId: string;
  }): Promise<number> {
    // remove promise. I just want to update the executioncontextId
    return await new Promise((resolve) => {
      cdp.Runtime.addEventListener('executionContextCreated', (fullData: { params: any }) => {
        const { context } = fullData.params;
        if (context.name === 'kaku-target-world' && context.auxData.frameId === targetId) {
          cdp?.Runtime.removeEventListener('executionContextCreated', (e) => {});
          resolve(context.id);
        }
      });
    });
  }

  private async attachControlAndTargetTabs({
    cdp,
    targetId,
    controlTabTargetId,
  }: {
    cdp: CDP;
    targetId: string;
    controlTabTargetId: string;
  }) {
    //Handle attaching to control and target tabs
    const attachToControlTargetResponse = await cdp.Target.attachToTarget({
      targetId: controlTabTargetId,
      flatten: true,
    });

    const attachToMainTargetResponse = await cdp.Target.attachToTarget({
      targetId: targetId,
      flatten: true,
    });

    // Enable required domains for both tabs
    this.log('About to enable requirements');
    await this.enableDomainsForBothTabs({
      cdp: cdp,
      targetSessionId: attachToMainTargetResponse.sessionId,
      controlTabSessionId: attachToControlTargetResponse.sessionId,
    });

    return {
      targetSessionId: attachToMainTargetResponse.sessionId,
      controlTabSessionId: attachToControlTargetResponse.sessionId,
    };
  }

  /**
   * Inject control tab scripts early using addScriptToEvaluateOnNewDocument
   * This ensures scripts persist across page navigations
   */
  private async injectControlTabScriptsEarly({
    cdp,
    targetId,
    controlTabSessionId,
    browserSessionWSEndpoint,
  }: {
    cdp: CDP;
    targetId: string;
    controlTabSessionId: string;
    browserSessionWSEndpoint: string;
  }): Promise<void> {
    const crossTabCommUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'cross-tab-communicator.min.js',
    );

    const persistentCDPControllerUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'persistent-cdp-controller.min.js',
    );

    try {
      // Fetch script contents for injection
      const [crossTabCommScript, persistentCDPControllerScript] = await Promise.all([
        fetch(crossTabCommUrl).then((r) => r.text()),
        fetch(persistentCDPControllerUrl).then((r) => r.text()),
      ]);

      // the delay is added to wait for the persistentCDP Controller script to be available
      const initScript = `
        (async () => {
          while (!window.persistentCDPController) {
            await new Promise(resolve => setTimeout(resolve, 10));
          }
          await window.persistentCDPController.init(
            '${browserSessionWSEndpoint}',
            '${targetId}'
          );

        })();
      `;

      // Inject scripts using addScriptToEvaluateOnNewDocument for persistence
      await Promise.all([
        cdp.Page.addScriptToEvaluateOnNewDocument(
          {
            source: wrapForMainFrameOnly(crossTabCommScript),
            worldName: 'kaku-control-world',
          },
          controlTabSessionId,
        ),
        cdp.Page.addScriptToEvaluateOnNewDocument(
          {
            source: wrapForMainFrameOnly(persistentCDPControllerScript),
            worldName: 'kaku-control-world',
          },
          controlTabSessionId,
        ),
        cdp.Page.addScriptToEvaluateOnNewDocument(
          {
            source: wrapForMainFrameOnly(initScript),
            worldName: 'kaku-control-world',
          },
          controlTabSessionId,
        ),
      ]);
    } catch (error) {
      this.error('Error injecting control tab scripts early:', error);
      throw error;
    }
  }

  private async injectBrowserControllerProxyInTarget({
    cdp,
    targetSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
  }) {
    const browserControllerProxyUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'browser-controller-proxy.min.js',
    );
    const piiRedactorUrl = getHashedScriptUrl(this.env.KAKU_API_ENDPOINT, 'pii-redactor.min.js');

    const [browserControllerProxyScript, piiRedactorScript] = await Promise.all([
      fetch(browserControllerProxyUrl).then((r) => r.text()),
      fetch(piiRedactorUrl).then((r) => r.text()),
    ]);

    // These scripts will persist across page navigations
    await Promise.all([
      cdp!.Page.addScriptToEvaluateOnNewDocument(
        {
          source: piiRedactorScript,
          worldName: 'kaku-target-world',
        },
        targetSessionId,
      ),
      cdp!.Page.addScriptToEvaluateOnNewDocument(
        {
          source: browserControllerProxyScript,
          worldName: 'kaku-target-world',
        },
        targetSessionId,
      ),
    ]);
  }

  /**
   * Enable required CDP domains for both control and target tabs
   */
  private async enableDomainsForBothTabs({
    cdp,
    controlTabSessionId,
    targetSessionId,
  }: {
    cdp: CDP;
    controlTabSessionId: string;
    targetSessionId: string;
  }): Promise<void> {
    // Enable domains for control tab
    await Promise.all([
      cdp.Page.enable(undefined, controlTabSessionId),
      cdp.Runtime.enable(undefined, controlTabSessionId),
    ]);

    // Enable domains for target tab
    await Promise.all([
      cdp.Page.enable(undefined, targetSessionId),
      cdp.Runtime.enable(undefined, targetSessionId),
    ]);

    // Disable content security policy for both tabs
    await Promise.all([
      cdp.Page.setBypassCSP({ enabled: true }, controlTabSessionId),
      cdp.Page.setBypassCSP({ enabled: true }, targetSessionId),
    ]);

    await Promise.all([cdp.WebAuthn.enable(undefined, targetSessionId)]);

    const virtualAuthenticatorOptions = {
      protocol: 'ctap2' as const,
      transport: 'internal' as const,
      hasResidentKey: true,
      hasUserVerification: true,
      isUserVerified: true,
      automaticPresenceSimulation: true,
    };
    await cdp.WebAuthn.addVirtualAuthenticator(
      { options: virtualAuthenticatorOptions },
      targetSessionId,
    );
  }

  private async handleBrowserError(error: any): Promise<void> {
    this.error('An error occurred while setting up the browser session:', error);
    if (error instanceof Error) {
      await ErrorService.handleBrowserConnectionError(
        error,
        {
          ...this.eventPayload,
          referenceId: this.eventPayload.linkId,
        },
        this.env,
      );
    }
  }

  private async waitForPageLoad({
    cdp,
    targetSessionId,
  }: {
    cdp: CDP;
    targetSessionId: string;
  }): Promise<boolean | null> {
    if (!cdp || !targetSessionId) return Promise.resolve(null);
    return await new Promise<boolean | null>((resolve) => {
      const handler = () => {
        this.log('Page Loaded event fired', { pageLoaded: true });
        resolve(true);
      };
      cdp?.Page.addEventListener('loadEventFired', handler);
    });
  }
}
