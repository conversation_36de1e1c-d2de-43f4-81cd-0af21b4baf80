import { ConnectionsWorkflowParams } from '../types';
import { WorkflowStepName } from '../types/WorkflowStepName';
import { ErrorService } from '../../common/error';
import { WorkflowStep } from 'cloudflare:workers';
import { defaultWorkflowNoRetryConfig } from '../utils/constants';

type ErrorHandler = (error: any) => Promise<void>;

export class WorkflowStepsManager {
  private readonly env: Env;
  private readonly eventPayload: ConnectionsWorkflowParams;
  private readonly step: WorkflowStep;
  private readonly errorHandler: ErrorHandler;

  constructor({
    env,
    step,
    eventPayload,
    errorHandler,
  }: {
    env: Env;
    step: WorkflowStep;
    eventPayload: ConnectionsWorkflowParams;
    errorHandler: ErrorHandler;
  }) {
    this.step = step;
    this.eventPayload = eventPayload;
    this.env = env;
    this.errorHandler = errorHandler;
  }

  async tryRunStep<S extends Rpc.Serializable<S>>(
    workflowStepName: WorkflowStepName,
    stepCallback: () => Promise<S>,
  ): Promise<S> {
    try {
      return await this.runStep(workflowStepName, stepCallback);
    } catch (e) {
      await this.handleWorkflowStepError(workflowStepName, e);
      throw e;
    }
  }

  async runStep<S extends Rpc.Serializable<S>>(
    workflowStepName: WorkflowStepName,
    stepCallback: () => Promise<S>,
  ): Promise<S> {
    return await this.step.do(workflowStepName, defaultWorkflowNoRetryConfig, stepCallback);
  }

  private async handleWorkflowStepError(
    workflowStepName: WorkflowStepName,
    error: any,
  ): Promise<void> {
    await ErrorService.handleWorkflowStepError(
      workflowStepName,
      error,
      {
        ...this.eventPayload,
        referenceId: this.eventPayload.linkId,
      },
      this.env,
    );
    await this.errorHandler(error);
  }
}
